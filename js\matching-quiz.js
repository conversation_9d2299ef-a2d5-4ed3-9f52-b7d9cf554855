// Matching Quiz Game Logic
class MatchingQuiz {
    constructor() {
        this.currentQuestionIndex = 0;
        this.wrongAnswers = 0;
        this.timer = null;
        this.timeLeft = 45;
        this.gameActive = false;
        this.gameData = [];
        this.totalLives = 3;
        this.totalExpEarned = 0;
        this.powerUpsUsed = {
            addTime: false,
            showAnswer: false
        };
        this.currentAnswers = {}; // Track current question answers
        this.selectedElement = null; // For keyboard navigation
        this.level = parseInt(document.body.dataset.level) || 2;
        this.currentScore = 0;
        this.correctCount = 0;
        this.streakCount = 0;
        this.maxStreak = 0;
        
        this.initializeElements();
        this.initializeEventListeners();
        this.loadQuestionsFromDatabase();
    }

    initializeElements() {
        // DOM Elements
        this.questionElement = document.getElementById('question');
        this.answerPoolElement = document.getElementById('answer-pool');
        this.dropZonesElement = document.getElementById('drop-zones');
        this.nextButton = document.getElementById('next-btn');
        this.backButton = document.getElementById('back-btn');
        this.timeElement = document.getElementById('time');
        this.livesElement = document.getElementById('lives');
        this.currentQuestionElement = document.getElementById('current-question');
        this.totalQuestionsElement = document.getElementById('total-questions');
        this.currentScoreElement = document.getElementById('current-score');
        this.correctCountElement = document.getElementById('correct-count');
        this.streakCountElement = document.getElementById('streak-count');
        this.progressFillElement = document.getElementById('progress-fill');
        this.gameArea = document.getElementById('game-area');
        this.instructionModal = document.getElementById('instruction-modal');
        this.startButton = document.getElementById('start-btn');
        this.resultsModal = document.getElementById('results-modal');
        this.volumeSlider = document.getElementById('volume-slider');
        this.volumeValue = document.getElementById('volume-value');

        // Power-up buttons
        this.addTimeBtn = document.getElementById('addTimeBtn');
        this.showAnswerBtn = document.getElementById('show-answer');

        // Sound Effects
        this.correctSound = new Audio('../../sounds/correct2.mp3');
        this.wrongSound = new Audio('../../sounds/wrong2.mp3');
        this.breakSound = new Audio('../../sounds/break2.mp3');
        this.successSound = new Audio('../../sounds/success.mp3');

        // Set initial volume
        this.setVolume(0.3);
    }

    initializeEventListeners() {
        // Start button
        this.startButton.addEventListener('click', () => this.startGame());

        // Navigation buttons
        this.nextButton.addEventListener('click', () => this.nextQuestion());
        this.backButton.addEventListener('click', () => this.previousQuestion());

        // Power-up buttons
        this.addTimeBtn.addEventListener('click', () => this.addTime());
        this.showAnswerBtn.addEventListener('click', () => this.showAnswer());

        // Volume control
        this.volumeSlider.addEventListener('input', (e) => {
            const volume = e.target.value / 100;
            this.setVolume(volume);
            this.volumeValue.textContent = e.target.value + '%';
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeyboardNavigation(e));

        // Modal buttons
        document.getElementById('play-again-btn')?.addEventListener('click', () => this.restartGame());
        document.getElementById('back-to-menu-btn')?.addEventListener('click', () => this.backToMenu());
    }

    async loadQuestionsFromDatabase() {
        try {
            const response = await fetch(`../../php/game2.php`);
            const data = await response.json();

            if (data.success && data.data.length > 0) {
                this.gameData = this.processMatchingQuestions(data.data);
                this.totalQuestionsElement.textContent = this.gameData.length;
                this.totalLives = Math.max(3, Math.floor(this.gameData.length * 0.3));
                this.updateLivesDisplay();
                console.log(`Loaded ${this.gameData.length} matching questions from database`);
            } else {
                console.warn('No questions found in database, using fallback questions');
                this.loadFallbackQuestions();
            }
        } catch (error) {
            console.error('Error loading questions:', error);
            this.loadFallbackQuestions();
        }
    }

    processMatchingQuestions(rawData) {
        // Convert database format to matching format
        return rawData.map(item => ({
            question: item.question_text,
            pairs: [
                { term: item.option1, definition: item.option2 },
                { term: item.option3, definition: item.option4 }
            ],
            correctAnswers: {
                [item.option1]: item.option2,
                [item.option3]: item.option4
            }
        }));
    }

    loadFallbackQuestions() {
        this.gameData = [
            {
                question: "Match the programming concepts with their definitions:",
                pairs: [
                    { term: "Variable", definition: "A storage location with a name" },
                    { term: "Function", definition: "A reusable block of code" }
                ],
                correctAnswers: {
                    "Variable": "A storage location with a name",
                    "Function": "A reusable block of code"
                }
            },
            {
                question: "Match the data types with their examples:",
                pairs: [
                    { term: "String", definition: "\"Hello World\"" },
                    { term: "Integer", definition: "42" }
                ],
                correctAnswers: {
                    "String": "\"Hello World\"",
                    "Integer": "42"
                }
            }
        ];
        this.totalQuestionsElement.textContent = this.gameData.length;
        this.totalLives = 3;
        this.updateLivesDisplay();
    }

    startGame() {
        this.instructionModal.style.display = 'none';
        this.gameActive = true;
        this.currentQuestionIndex = 0;
        this.wrongAnswers = 0;
        this.timeLeft = 45;
        this.currentScore = 0;
        this.correctCount = 0;
        this.streakCount = 0;
        this.maxStreak = 0;
        this.resetPowerUps();
        this.displayQuestion();
        this.startTimer();
        this.updateLivesDisplay();
        this.updateScoreDisplay();
        this.showKeyboardHint();
    }

    displayQuestion() {
        if (this.currentQuestionIndex >= this.gameData.length) {
            this.endGame(true);
            return;
        }

        const currentQuestion = this.gameData[this.currentQuestionIndex];
        this.questionElement.textContent = currentQuestion.question;
        this.currentQuestionElement.textContent = this.currentQuestionIndex + 1;
        
        this.createAnswerPool(currentQuestion);
        this.createDropZones(currentQuestion);
        this.updateNavigationButtons();
        this.currentAnswers = {};
    }

    createAnswerPool(question) {
        this.answerPoolElement.innerHTML = '';
        
        // Create all possible answers (definitions) and shuffle them
        const allAnswers = question.pairs.map(pair => pair.definition);
        this.shuffleArray(allAnswers);

        allAnswers.forEach((answer, index) => {
            const answerElement = this.createAnswerElement(answer, index);
            this.answerPoolElement.appendChild(answerElement);
        });
    }

    createAnswerElement(answer, index) {
        const element = document.createElement('div');
        element.className = 'answer-option';
        element.textContent = answer;
        element.draggable = true;
        element.dataset.answer = answer;
        element.dataset.index = index;
        element.tabIndex = 0;
        element.setAttribute('role', 'button');
        element.setAttribute('aria-label', `Answer option: ${answer}`);

        // Drag events
        element.addEventListener('dragstart', (e) => this.handleDragStart(e));
        element.addEventListener('dragend', (e) => this.handleDragEnd(e));

        // Touch events for mobile
        element.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        element.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        element.addEventListener('touchend', (e) => this.handleTouchEnd(e));

        // Keyboard events
        element.addEventListener('keydown', (e) => this.handleAnswerKeydown(e));

        return element;
    }

    createDropZones(question) {
        this.dropZonesElement.innerHTML = '';

        question.pairs.forEach((pair, index) => {
            const dropZone = this.createDropZone(pair.term, index);
            this.dropZonesElement.appendChild(dropZone);
        });
    }

    createDropZone(term, index) {
        const container = document.createElement('div');
        container.className = 'drop-zone-container';

        const label = document.createElement('div');
        label.className = 'drop-zone-label';
        label.textContent = term;
        label.setAttribute('id', `term-label-${index}`);

        const dropZone = document.createElement('div');
        dropZone.className = 'drop-zone';
        dropZone.dataset.term = term;
        dropZone.dataset.index = index;
        dropZone.innerHTML = '<span class="placeholder-text">Drop answer here</span>';
        dropZone.tabIndex = 0;
        dropZone.setAttribute('role', 'button');
        dropZone.setAttribute('aria-label', `Drop zone for ${term}. Press Enter to place selected answer here.`);
        dropZone.setAttribute('aria-describedby', `term-label-${index}`);

        // Drop events
        dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        dropZone.addEventListener('drop', (e) => this.handleDrop(e));
        dropZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));

        // Click to remove answer
        dropZone.addEventListener('click', (e) => this.handleDropZoneClick(e));

        container.appendChild(label);
        container.appendChild(dropZone);
        return container;
    }

    // Drag and Drop Event Handlers
    handleDragStart(e) {
        if (e.target.classList.contains('used')) return;
        
        e.target.classList.add('dragging');
        e.dataTransfer.setData('text/plain', e.target.dataset.answer);
        e.dataTransfer.setData('text/index', e.target.dataset.index);
        e.dataTransfer.effectAllowed = 'move';
    }

    handleDragEnd(e) {
        e.target.classList.remove('dragging');
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        e.target.classList.add('drag-over');
    }

    handleDragLeave(e) {
        e.target.classList.remove('drag-over');
    }

    handleDrop(e) {
        e.preventDefault();
        e.target.classList.remove('drag-over');
        
        const answer = e.dataTransfer.getData('text/plain');
        const answerIndex = e.dataTransfer.getData('text/index');
        const term = e.target.dataset.term;
        
        this.placeAnswer(term, answer, answerIndex, e.target);
    }

    placeAnswer(term, answer, answerIndex, dropZone) {
        // Remove any existing answer in this drop zone
        this.removeAnswerFromDropZone(dropZone);
        
        // Place new answer
        dropZone.innerHTML = `<div class="dropped-answer" data-answer="${answer}">${answer}</div>`;
        dropZone.classList.add('filled');
        
        // Mark answer as used
        const answerElement = this.answerPoolElement.querySelector(`[data-index="${answerIndex}"]`);
        if (answerElement) {
            answerElement.classList.add('used');
        }
        
        // Store the answer
        this.currentAnswers[term] = answer;
        
        // Check if all answers are placed
        this.checkAllAnswersPlaced();
    }

    removeAnswerFromDropZone(dropZone) {
        const existingAnswer = dropZone.querySelector('.dropped-answer');
        if (existingAnswer) {
            const answer = existingAnswer.dataset.answer;
            
            // Find and unmark the answer in the pool
            const answerElement = this.answerPoolElement.querySelector(`[data-answer="${answer}"]`);
            if (answerElement) {
                answerElement.classList.remove('used');
            }
            
            // Remove from current answers
            const term = dropZone.dataset.term;
            delete this.currentAnswers[term];
        }
        
        dropZone.innerHTML = '<span class="placeholder-text">Drop answer here</span>';
        dropZone.classList.remove('filled', 'correct', 'incorrect');
    }

    handleDropZoneClick(e) {
        if (e.target.closest('.dropped-answer')) {
            this.removeAnswerFromDropZone(e.currentTarget);
            this.checkAllAnswersPlaced();
        }
    }

    checkAllAnswersPlaced() {
        const currentQuestion = this.gameData[this.currentQuestionIndex];
        const requiredAnswers = currentQuestion.pairs.length;
        const placedAnswers = Object.keys(this.currentAnswers).length;
        
        if (placedAnswers === requiredAnswers) {
            this.nextButton.disabled = false;
            this.validateAnswers();
        } else {
            this.nextButton.disabled = true;
        }
    }

    validateAnswers() {
        const currentQuestion = this.gameData[this.currentQuestionIndex];
        let correctCount = 0;
        
        Object.entries(this.currentAnswers).forEach(([term, answer]) => {
            const dropZone = this.dropZonesElement.querySelector(`[data-term="${term}"]`);
            const isCorrect = currentQuestion.correctAnswers[term] === answer;
            
            if (isCorrect) {
                dropZone.classList.add('correct');
                correctCount++;
            } else {
                dropZone.classList.add('incorrect');
            }
        });
        
        if (correctCount === currentQuestion.pairs.length) {
            this.playSound(this.correctSound);
            this.streakCount++;
            this.maxStreak = Math.max(this.maxStreak, this.streakCount);
            this.correctCount++;
            this.currentScore += 100 + (this.streakCount * 10); // Base score + streak bonus
        } else {
            this.playSound(this.wrongSound);
            this.streakCount = 0; // Reset streak on wrong answer
            this.wrongAnswers++;

            if (this.wrongAnswers >= this.totalLives) {
                this.endGame(false);
                return;
            }
        }

        this.updateLivesDisplay();
        this.updateScoreDisplay();
        this.updateProgressBar();
    }

    // Utility methods
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    setVolume(volume) {
        this.correctSound.volume = volume;
        this.wrongSound.volume = volume;
        this.breakSound.volume = volume;
        this.successSound.volume = volume;
    }

    updateLivesDisplay() {
        const remainingLives = this.totalLives - this.wrongAnswers;
        this.livesElement.textContent = remainingLives;
    }

    updateNavigationButtons() {
        this.backButton.disabled = this.currentQuestionIndex === 0;
        this.nextButton.disabled = true; // Will be enabled when all answers are placed
    }

    updateScoreDisplay() {
        if (this.currentScoreElement) {
            this.currentScoreElement.textContent = this.currentScore;
            this.currentScoreElement.classList.add('updated');
            setTimeout(() => {
                this.currentScoreElement.classList.remove('updated');
            }, 600);
        }

        if (this.correctCountElement) {
            this.correctCountElement.textContent = this.correctCount;
        }

        if (this.streakCountElement) {
            this.streakCountElement.textContent = this.streakCount;
            if (this.streakCount > 0) {
                this.streakCountElement.classList.add('updated');
                setTimeout(() => {
                    this.streakCountElement.classList.remove('updated');
                }, 600);
            }
        }
    }

    updateProgressBar() {
        if (this.progressFillElement && this.gameData.length > 0) {
            const progress = ((this.currentQuestionIndex + 1) / this.gameData.length) * 100;
            this.progressFillElement.style.width = `${progress}%`;
        }
    }

    // Navigation methods
    nextQuestion() {
        if (this.currentQuestionIndex < this.gameData.length - 1) {
            this.currentQuestionIndex++;
            this.timeLeft = 45;
            this.displayQuestion();
        } else {
            this.endGame(true);
        }
    }

    previousQuestion() {
        if (this.currentQuestionIndex > 0) {
            this.currentQuestionIndex--;
            this.timeLeft = 45;
            this.displayQuestion();
        }
    }

    // Timer methods
    startTimer() {
        this.timer = setInterval(() => {
            this.timeLeft--;
            this.timeElement.textContent = this.timeLeft;

            if (this.timeLeft <= 10) {
                this.timeElement.classList.add('warning');
            }

            if (this.timeLeft <= 0) {
                this.handleTimeUp();
            }
        }, 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    handleTimeUp() {
        this.stopTimer();
        this.wrongAnswers++;
        this.playSound(this.wrongSound);
        this.updateRobotPosition();

        if (this.wrongAnswers >= this.totalLives) {
            this.endGame(false);
        } else {
            this.nextQuestion();
        }
    }

    // Power-up methods
    addTime() {
        if (this.powerUpsUsed.addTime) return;

        this.timeLeft += 15;
        this.timeElement.textContent = this.timeLeft;
        this.timeElement.classList.add('time-added');

        setTimeout(() => {
            this.timeElement.classList.remove('time-added');
        }, 500);

        this.powerUpsUsed.addTime = true;
        this.addTimeBtn.disabled = true;
        this.addTimeBtn.innerHTML = '<span class="power-up-icon">⏱️</span><span class="power-up-text">+15 Used</span>';
    }

    showAnswer() {
        if (this.powerUpsUsed.showAnswer) return;

        const currentQuestion = this.gameData[this.currentQuestionIndex];
        const firstPair = currentQuestion.pairs[0];

        // Show the first correct answer
        const term = firstPair.term;
        const correctAnswer = currentQuestion.correctAnswers[term];
        const dropZone = this.dropZonesElement.querySelector(`[data-term="${term}"]`);

        if (dropZone && !dropZone.classList.contains('filled')) {
            // Find the answer in the pool
            const answerElement = this.answerPoolElement.querySelector(`[data-answer="${correctAnswer}"]`);
            if (answerElement) {
                const answerIndex = answerElement.dataset.index;
                this.placeAnswer(term, correctAnswer, answerIndex, dropZone);
            }
        }

        this.powerUpsUsed.showAnswer = true;
        this.showAnswerBtn.disabled = true;
        this.showAnswerBtn.innerHTML = '<span class="power-up-icon">💡</span><span class="power-up-text">Used</span>';
    }

    resetPowerUps() {
        this.powerUpsUsed = {
            addTime: false,
            showAnswer: false
        };

        this.addTimeBtn.disabled = false;
        this.addTimeBtn.innerHTML = '<span class="power-up-icon">⏱️</span><span class="power-up-text">+15 Time</span>';

        this.showAnswerBtn.disabled = false;
        this.showAnswerBtn.innerHTML = '<span class="power-up-icon">💡</span><span class="power-up-text">Show Answer</span>';
    }

    // Touch events for mobile support
    handleTouchStart(e) {
        if (e.target.classList.contains('used')) return;

        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
        this.draggedElement = e.target;
        e.target.classList.add('dragging');
    }

    handleTouchMove(e) {
        e.preventDefault();
        if (!this.draggedElement) return;

        const touch = e.touches[0];
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);

        // Remove previous drag-over states
        document.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));

        // Add drag-over to current element if it's a drop zone
        if (elementBelow && elementBelow.classList.contains('drop-zone')) {
            elementBelow.classList.add('drag-over');
        }
    }

    handleTouchEnd(e) {
        if (!this.draggedElement) return;

        const touch = e.changedTouches[0];
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);

        // Clean up
        this.draggedElement.classList.remove('dragging');
        document.querySelectorAll('.drag-over').forEach(el => el.classList.remove('drag-over'));

        // Handle drop
        if (elementBelow && elementBelow.classList.contains('drop-zone')) {
            const answer = this.draggedElement.dataset.answer;
            const answerIndex = this.draggedElement.dataset.index;
            const term = elementBelow.dataset.term;

            this.placeAnswer(term, answer, answerIndex, elementBelow);
        }

        this.draggedElement = null;
    }

    // Keyboard navigation
    handleKeyboardNavigation(e) {
        if (!this.gameActive) return;

        switch (e.key) {
            case 'Tab':
                // Let default tab behavior work
                break;
            case 'Enter':
            case ' ':
                if (e.target.classList.contains('answer-option') && !e.target.classList.contains('used')) {
                    this.selectAnswerForKeyboard(e.target);
                } else if (e.target.classList.contains('drop-zone')) {
                    this.placeSelectedAnswer(e.target);
                }
                e.preventDefault();
                break;
            case 'Escape':
                this.selectedElement = null;
                document.querySelectorAll('.keyboard-selected').forEach(el => el.classList.remove('keyboard-selected'));
                break;
        }
    }

    selectAnswerForKeyboard(element) {
        // Clear previous selection
        document.querySelectorAll('.keyboard-selected').forEach(el => el.classList.remove('keyboard-selected'));

        // Select new element
        this.selectedElement = element;
        element.classList.add('keyboard-selected');
    }

    placeSelectedAnswer(dropZone) {
        if (!this.selectedElement) return;

        const answer = this.selectedElement.dataset.answer;
        const answerIndex = this.selectedElement.dataset.index;
        const term = dropZone.dataset.term;

        this.placeAnswer(term, answer, answerIndex, dropZone);

        // Clear selection
        this.selectedElement.classList.remove('keyboard-selected');
        this.selectedElement = null;
    }

    handleAnswerKeydown(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            this.selectAnswerForKeyboard(e.target);
            e.preventDefault();
        }
    }

    // Game end methods
    endGame(won) {
        this.gameActive = false;
        this.stopTimer();

        const correctAnswers = this.gameData.length - this.wrongAnswers;
        const accuracy = (correctAnswers / this.gameData.length) * 100;

        let stars = 0;
        let expGained = 0;

        if (won) {
            this.playSound(this.successSound);
            if (accuracy >= 90) {
                stars = 3;
                expGained = 100;
            } else if (accuracy >= 70) {
                stars = 2;
                expGained = 75;
            } else {
                stars = 1;
                expGained = 50;
            }
        } else {
            this.playSound(this.breakSound);
            expGained = Math.max(10, correctAnswers * 5);
        }

        this.totalExpEarned = expGained;
        this.showResults(won, stars, expGained, correctAnswers);
    }

    showResults(won, stars, expGained, correctAnswers) {
        const resultTitle = document.getElementById('result-title');
        const resultStars = document.getElementById('result-stars');
        const resultMessage = document.getElementById('result-message');
        const expContainer = document.querySelector('.exp-container .exp-text');

        resultTitle.textContent = won ? 'Mission Complete!' : 'Mission Failed!';
        resultStars.textContent = '⭐'.repeat(stars);
        resultMessage.textContent = `You got ${correctAnswers} out of ${this.gameData.length} questions correct!`;

        if (expContainer) {
            expContainer.textContent = `+${expGained} EXP`;
        }

        this.resultsModal.style.display = 'flex';

        // Save progress to database
        this.saveProgress(won, stars, expGained);
    }

    async saveProgress(won, stars, expGained) {
        try {
            const response = await fetch('../../php/savetodb.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    level: this.level,
                    score: won ? 100 : Math.max(10, (this.gameData.length - this.wrongAnswers) * 10),
                    stars: stars,
                    exp: expGained
                })
            });

            const result = await response.json();
            console.log('Progress saved:', result);
        } catch (error) {
            console.error('Error saving progress:', error);
        }
    }

    restartGame() {
        this.resultsModal.style.display = 'none';
        this.instructionModal.style.display = 'flex';
        this.currentQuestionIndex = 0;
        this.wrongAnswers = 0;
        this.timeLeft = 45;
        this.resetPowerUps();
        this.updateLivesDisplay();

        if (this.robotElement) {
            this.robotElement.style.transform = 'translateY(0)';
            this.robotElement.classList.remove('danger');
        }
    }

    backToMenu() {
        window.location.href = '../mainpage.html';
    }

    // Keyboard hint system
    showKeyboardHint() {
        const hint = document.createElement('div');
        hint.className = 'keyboard-hint show';
        hint.innerHTML = 'Keyboard: Tab to navigate, Enter/Space to select answers, Escape to deselect';
        document.body.appendChild(hint);

        setTimeout(() => {
            hint.classList.remove('show');
            setTimeout(() => {
                if (hint.parentNode) {
                    hint.parentNode.removeChild(hint);
                }
            }, 300);
        }, 4000);
    }

    // Performance optimization - debounce touch events
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Error handling for audio
    playSound(sound) {
        try {
            if (sound && typeof sound.play === 'function') {
                sound.play().catch(e => {
                    console.warn('Audio playback failed:', e);
                });
            }
        } catch (error) {
            console.warn('Audio error:', error);
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new MatchingQuiz();
});
