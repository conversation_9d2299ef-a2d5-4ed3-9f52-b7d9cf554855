/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0b0b23;
    color: #fff;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    overflow-x: hidden;
}

/* Game Container */
.game-container {
    width: 100%;
    max-width: 85vw;
    min-height: 600px;
    max-height: fit-content;
    position: relative;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.modal-content {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.3);
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: modalAppear 0.5s ease-out;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

#result-title {
    color: #4fc3f7;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.stars {
    font-size: 2.5rem;
    margin: 20px 0;
    letter-spacing: 10px;
}

#result-message {
    color: #fff;
    font-size: 1.2rem;
    margin: 20px 0;
    line-height: 1.5;
}

.exp-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: expPulse 2s infinite;
}

@keyframes expPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 15px rgba(79, 195, 247, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 25px rgba(79, 195, 247, 0.4);
    }
}

.exp-icon {
    font-size: 1.8rem;
    animation: sparkle 1.5s infinite;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(15deg);
        opacity: 0.8;
    }
}

.exp-text {
    color: #ffeb3b;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.5);
}

.modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

#start-btn, .next-btn, .continue-btn, .try-again-btn, .exit-btn, .power-up-btn {
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 150px;
    padding: 15px 30px;
}

#start-btn, .continue-btn {
    background: linear-gradient(145deg, #4caf50, #388e3c);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

#start-btn:hover, .continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.exit-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.exit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.try-again-btn {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.try-again-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Responsive styles for modal */
@media (max-width: 480px) {
    .modal-content {
        padding: 20px;
    }

    #result-title {
        font-size: 2rem;
    }

    .stars {
        font-size: 2rem;
    }

    #result-message {
        font-size: 1rem;
    }

    .exp-container {
        padding: 10px;
    }

    .exp-icon {
        font-size: 1.5rem;
    }

    .exp-text {
        font-size: 1.2rem;
    }

    .modal-buttons {
        flex-direction: column;
        gap: 15px;
    }

    #start-btn, .next-btn, .continue-btn, .try-again-btn, .exit-btn, .power-up-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 1.1rem;
    }
}

/* Game Area */
.game-area {
    width: 100%;
    height: 100%;
    display: flex;
    background-color: #1a1a3a;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.2);
}

.quiz-section {
    flex: 2;
    padding: 30px;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    font-size: 1.2rem;
}

.header .timer, .header .lives {
    background: rgba(79, 195, 247, 0.1);
    border: 1.5px solid rgba(79, 195, 247, 0.2);
    border-radius: 12px;
    padding: 8px 18px;
    font-size: 1.1rem;
    font-weight: bold;
    color: #ffd700;
    box-shadow: 0 2px 8px rgba(79, 195, 247, 0.08);
    margin-right: 10px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.header .lives {
    color: #f44336;
    background: rgba(244, 67, 54, 0.08);
    border-color: rgba(244, 67, 54, 0.2);
}

.question {
    font-size: 20px;
    line-height: 1.4;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    flex: 1;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    border-radius: 20px;
    border: 2px solid #2fa3d7;
    border-left: 6px solid #2fa3d7;
    margin: 0 0 30px 0;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.question::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background-color: #2fa3d7;
    border-radius: 20px;
    z-index: -1;
    opacity: 0.3;
    animation: questionGlow 3s ease-in-out infinite;
}

@keyframes questionGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.option {
    padding: 15px;
    background-color: #2a2a4a;
    border: none;
    border-radius: 8px;
    color: #fff;
    font-size: 1.1rem;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s;
}

.option:hover {
    background-color: #3a3a5a;
    transform: translateX(5px);
}

.option.selected {
    background-color: #4fc3f7 !important;
    color: #0b0b23 !important;
    font-weight: bold;
    border-left: 5px solid #ffeb3b;
}

.next-btn {
    padding: 15px;
    background-color: #4fc3f7;
    color: #0b0b23;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    margin-top: auto;
    transition: all 0.3s;
}

.next-btn:hover {
    background-color: #3fb3e7;
    transform: scale(1.02);
}

.next-btn:disabled {
    background-color: #2a2a4a;
    color: #555;
    cursor: not-allowed;
    transform: none;
}

/* Power-ups styled like .lifeline in game3 */
.power-ups {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 18px;
    background: rgba(79, 195, 247, 0.08);
    border-radius: 15px;
    padding: 10px 0;
}

.power-up-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    min-width: 80px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    box-shadow: 0 4px 0 #2fa3d7;
    font-size: 1rem;
    gap: 4px;
}

.power-up-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

.power-up-btn:disabled {
    background: #2fa3d7;
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.power-up-icon {
    font-size: 20px;
    margin-bottom: 2px;
}

.power-up-text {
    font-size: 12px;
}

/* +10 Time Button */
.add-time, #eliminate-wrong, #show-answer {
    background-color: #2fa3d7;
    color: white;
}

/* Pulse animation for time addition */
@keyframes timeAddPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.time-added {
    animation: timeAddPulse 0.5s ease;
}

/* Eliminate Wrong Button */


/* Show Answer Button */

/* Show answer highlight effect */
.correct-answer {
    animation: highlightAnswer 0.3s ease-out;
    background-color: #2fa3d7 !important;
    color: white !important;
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.5) !important;
}

@keyframes highlightAnswer {
    0% { transform: scale(1); box-shadow: none; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1.05); }
}

/* Enhanced Robot Section */
.robot-section {
    flex: 1;
    background: radial-gradient(circle, #1a1a40, #0f0f2a);
    position: relative;
    display: flex;
    justify-content: center;
    padding-top: 50px;
    min-height: 500px;
    overflow: hidden;
}

.scene-container {
    position: relative;
    width: 350px;
    height: 400px;
}

/* Enhanced Gallows Design */
.gallows {
    position: relative;
    width: 30px;
    height: 320px;
    z-index: 1;
}

.gallows .base {
    position: absolute;
    bottom: 0;
    left: -35px;
    width: 100px;
    height: 20px;
    background: linear-gradient(to bottom, #8b5a2b, #6d4c28);
    border-radius: 5px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.5);
}

.gallows .pole {
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 30px;
    height: 300px;
    background: linear-gradient(to right, #8b5a2b, #6d4c28);
    border-radius: 5px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.3);
}

.gallows .beam {
    position: absolute;
    top: 0;
    left: 30px;
    width: 150px;
    height: 25px;
    background: linear-gradient(to bottom, #8b5a2b, #6d4c28);
    border-radius: 5px;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.3);
}

.gallows .support {
    position: absolute;
    top: 25px;
    left: 30px;
    width: 70px;
    height: 70px;
    background: transparent;
    border-left: 15px solid #6d4c28;
    border-top: 15px solid transparent;
    transform: rotate(45deg);
    transform-origin: top left;
}

.rope {
    position: absolute;
    top: 25px;
    left: 150px;
    width: 6px;
    height: 40px;
    background: linear-gradient(to right, #a98467, #8d6e63);
    z-index: 1;
    box-shadow: 2px 0 2px rgba(0, 0, 0, 0.2);
}

/* Enhanced Robot Design */
.robot {
    position: absolute;
    top: 70px;
    left: 125px;
    width: 100px;
    transition: all 0.5s ease;
    z-index: 2;
    transform-origin: top center;
    animation: sway 4s ease-in-out infinite;
}

@keyframes sway {
    0%, 100% { transform: rotate(-1deg); }
    50% { transform: rotate(1deg); }
}

/* Rope around neck */
.rope-around-neck {
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 10px;
    background: linear-gradient(to bottom, #a98467, #8d6e63);
    border-radius: 5px;
    z-index: 3;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    transition: all 0.5s ease;
}

/* Enhanced Head */
.robot .head {
    width: 70px;
    height: 70px;
    background: linear-gradient(145deg, #78909c, #546e7a);
    border-radius: 45% 45% 45% 45% / 45% 45% 45% 45%;
    margin: 0 auto;
    position: relative;
    border: 3px solid #455a64;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.3),
                0 5px 10px rgba(0, 0, 0, 0.2);
    overflow: visible;
}

.antenna {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 5px;
    height: 15px;
    background: #455a64;
    z-index: 2;
}

.antenna::after {
    content: '';
    position: absolute;
    top: -7px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #f44336;
    box-shadow: 0 0 10px #ff6659;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Enhanced Eyes */
.eyes {
    display: flex;
    justify-content: space-around;
    padding: 18px 5px 0;
}

.eye {
    width: 18px;
    height: 18px;
    background: linear-gradient(145deg, #ffffff, #e6e6e6);
    border-radius: 50%;
    border: 2px solid #37474f;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
    position: relative;
    overflow: hidden;
}

.pupil {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #000;
    border-radius: 50%;
    top: 4px;
    left: 4px;
    animation: blink 3s infinite;
}

.shine {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #fff;
    border-radius: 50%;
    top: 2px;
    left: 10px;
    opacity: 0.8;
}

@keyframes blink {
    0%, 96%, 98% { transform: scaleY(1); }
    97% { transform: scaleY(0.1); }
}

/* Enhanced Mouth */
.mouth {
    width: 30px;
    height: 10px;
    background: linear-gradient(to bottom, #e53935, #c62828);
    margin: 10px auto 0;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 -2px 3px rgba(0, 0, 0, 0.3);
}

.teeth {
    display: flex;
    justify-content: space-around;
    padding: 1px;
}

.teeth::before,
.teeth::after {
    content: '';
    width: 3px;
    height: 4px;
    background-color: #f5f5f5;
    border-radius: 1px;
}

/* Enhanced Body */
.body {
    width: 85px;
    height: 100px;
    background: linear-gradient(145deg, #78909c, #546e7a);
    margin: -5px auto 0;
    border-radius: 15px;
    border: 3px solid #455a64;
    position: relative;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2),
                0 5px 15px rgba(0, 0, 0, 0.2);
    overflow: visible;
}

.panel {
    position: absolute;
    width: 50px;
    height: 25px;
    background: linear-gradient(to bottom, #546e7a, #455a64);
    border-radius: 5px;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    border: 2px solid #37474f;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
}

.panel::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    top: 7px;
    left: 50%;
    transform: translateX(-50%);
}

.buttons {
    position: absolute;
    display: flex;
    justify-content: space-around;
    width: 50px;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
}

.button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #f44336;
    box-shadow: inset 0 -2px 3px #c62828, 
                0 2px 3px rgba(0, 0, 0, 0.3);
    animation: glow 2s infinite alternate;
}

.button:nth-child(1) {
    animation-delay: 0s;
}

.button:nth-child(2) {
    background: #ffeb3b;
    box-shadow: inset 0 -2px 3px #fbc02d, 
                0 2px 3px rgba(0, 0, 0, 0.3);
    animation-delay: 0.5s;
}

.button:nth-child(3) {
    background: #4caf50;
    box-shadow: inset 0 -2px 3px #388e3c, 
                0 2px 3px rgba(0, 0, 0, 0.3);
    animation-delay: 1s;
}

@keyframes glow {
    0% { opacity: 0.7; }
    100% { opacity: 1; box-shadow: 0 0 5px currentColor; }
}

/* Enhanced Arms */
.arms {
    position: absolute;
    width: 160px;
    top: 85px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    z-index: 1;
}

.arm {
    width: 15px;
    height: 60px;
    background: linear-gradient(to right, #78909c, #546e7a);
    border: 2px solid #455a64;
    border-radius: 6px;
    position: relative;
}

.arm.left {
    transform: rotate(15deg);
    transform-origin: top right;
}

.arm.right {
    transform: rotate(-15deg);
    transform-origin: top left;
}

/* Shoulder joint to connect arm to body */
.arm::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: linear-gradient(145deg, #607d8b, #546e7a);
    border: 2px solid #455a64;
    border-radius: 50%;
    top: -10px;
    z-index: -1;
}

.arm.left::before {
    right: -2px;
}

.arm.right::before {
    left: -2px;
}

.joint {
    position: absolute;
    width: 12px;
    height: 12px;
    background: linear-gradient(145deg, #607d8b, #546e7a);
    border: 2px solid #455a64;
    border-radius: 50%;
    z-index: 2;
}

.elbow {
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
}

.hand {
    position: absolute;
    width: 18px;
    height: 15px;
    background: linear-gradient(145deg, #607d8b, #546e7a);
    border: 2px solid #455a64;
    border-radius: 5px;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
    padding: 2px;
}

.finger {
    width: 3px;
    height: 6px;
    background: #455a64;
    border-radius: 2px;
}

/* Enhanced Legs */
.legs {
    position: absolute;
    width: 90px;
    top: 155px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    z-index: 0;
}

.leg {
    width: 18px;
    height: 65px;
    background: linear-gradient(to right, #78909c, #546e7a);
    border: 2px solid #455a64;
    border-radius: 6px;
    position: relative;
}

/* Hip joint to connect leg to body */
.leg::before {
    content: '';
    position: absolute;
    width: 22px;
    height: 22px;
    background: linear-gradient(145deg, #607d8b, #546e7a);
    border: 2px solid #455a64;
    border-radius: 50%;
    top: -11px;
    z-index: -1;
}

.leg.left::before {
    right: -2px;
}

.leg.right::before {
    left: -2px;
}

.knee {
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.foot {
    position: absolute;
    width: 25px;
    height: 10px;
    background: linear-gradient(145deg, #546e7a, #455a64);
    border: 2px solid #37474f;
    border-radius: 10px 10px 5px 5px;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

/* Add ambient animation */
@keyframes ambientLight {
    0%, 100% { box-shadow: 0 0 15px rgba(0, 150, 255, 0.2); }
    50% { box-shadow: 0 0 25px rgba(0, 150, 255, 0.4); }
}

.robot-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    background: radial-gradient(circle at 50% 150%, rgba(0, 150, 255, 0.1), transparent 80%);
    animation: ambientLight 4s infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .game-container {
        max-width: 95vw;
    }
    
    .scene-container {
        width: 300px;
        height: 350px;
    }
    
    .robot {
        width: 90px;
        left: 105px;
    }
}

@media (max-width: 768px) {
    .game-area {
        flex-direction: column;
        height: auto;
    }
    
    .quiz-section {
        padding: 20px;
    }
    
    .robot-section {
        height: 300px;
        min-height: 300px;
    }
    
    .scene-container {
        width: 250px;
        height: 300px;
    }
    
    .robot {
        width: 80px;
        left: 85px;
    }
    
    .question {
        font-size: 16px;
        padding: 15px;
    }
    
    .header .timer, .header .lives {
        font-size: 1rem;
        padding: 6px 10px;
    }
    
    .power-ups {
        gap: 10px;
        padding: 6px 0;
    }
    
    .power-up-btn {
        min-width: 60px;
        font-size: 0.95rem;
        padding: 8px 10px;
    }
    
    .modal-content {
        padding: 20px;
        border-radius: 12px;
    }
    
    .modal-buttons {
        flex-direction: column;
        gap: 15px;
        margin-top: 20px;
    }
}

@media (max-width: 480px) {
    .game-container {
        max-width: 100vw;
    }
    
    .question {
        font-size: 14px;
        padding: 10px;
    }
    
    .header .timer, .header .lives {
        font-size: 0.95rem;
        padding: 4px 6px;
    }
    
    .power-ups {
        gap: 6px;
        padding: 4px 0;
    }
    
    .power-up-btn {
        min-width: 48px;
        font-size: 0.9rem;
        padding: 6px 6px;
    }
    
    .modal-content {
        padding: 10px;
        border-radius: 8px;
    }
    
    .modal h2 {
        font-size: 1.5rem;
    }
    
    .scene-container {
        width: 200px;
        height: 250px;
    }
    
    .robot {
        width: 70px;
        left: 65px;
    }
    
    .header {
        font-size: 1rem;
    }
    
    .option {
        font-size: 0.9rem;
        padding: 10px;
    }
    
    .next-btn, #start-btn, #restart-btn {
        font-size: 1rem;
        padding: 12px 20px;
    }
}

/* Volume Control */
.volume-control {
    display: flex;
    align-items: center;
    margin-left: 15px;
    padding: 5px 10px;
    background-color: rgba(42, 42, 74, 0.5);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.volume-control:hover {
    background-color: rgba(42, 42, 74, 0.8);
}

.volume-control label {
    margin-right: 8px;
    font-size: 1rem;
    color: #ccc;
    cursor: pointer;
}

.volume-control input[type="range"] {
    width: 80px;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: #2a2a4a;
    border-radius: 2px;
    outline: none;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4fc3f7;
    cursor: pointer;
    transition: all 0.2s;
}

.volume-control input[type="range"]::-webkit-slider-thumb:hover {
    background: #3fb3e7;
    transform: scale(1.1);
}

.volume-control input[type="range"]::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4fc3f7;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

.volume-control input[type="range"]::-moz-range-thumb:hover {
    background: #3fb3e7;
    transform: scale(1.1);
}

.volume-control #volume-value {
    margin-left: 8px;
    min-width: 35px;
    font-size: 0.8rem;
    color: #ccc;
}

/* Responsive adjustments for volume control */
@media (max-width: 768px) {
    .volume-control {
        margin-top: 10px;
        margin-left: 0;
        width: 100%;
        justify-content: space-between;
    }
    
    .volume-control input[type="range"] {
        width: 60%;
    }
}

/* Instruction Modal Styles */
#instruction-title {
    color: #4fc3f7;
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.instructions {
    background: rgba(79, 195, 247, 0.1);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    margin-bottom: 30px;
    text-align: left;
}

.instructions p {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 20px;
    line-height: 1.6;
}

.instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instructions li {
    color: #fff;
    font-size: 1.1rem;
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
    line-height: 1.5;
}

.instructions li::before {
    content: '⚡';
    position: absolute;
    left: 0;
    color: #ffeb3b;
    font-size: 1.2rem;
    animation: sparkle 1.5s infinite;
}

#start-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    padding: 15px 40px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 #2fa3d7;
    margin-top: 20px;
}

#start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

#start-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.3);
}

/* Responsive styles for instruction modal */
@media (max-width: 480px) {
    #instruction-title {
        font-size: 2rem;
    }

    .instructions {
        padding: 20px;
    }

    .instructions p {
        font-size: 1.1rem;
    }

    .instructions li {
        font-size: 1rem;
        padding-left: 25px;
    }

    #start-btn {
        padding: 12px 30px;
        font-size: 1.1rem;
        min-width: 160px;
    }
}

/* Unified modal button styles for result modal (matching game3) */
.replay-btn, .main-menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.replay-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    box-shadow: 0 4px 0 #2fa3d7;
}
.replay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}
.main-menu-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: #fff;
    box-shadow: 0 4px 0 #b71c1c;
}
.main-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #b71c1c;
}